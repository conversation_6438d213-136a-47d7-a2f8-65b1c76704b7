#!/usr/bin/env python3
"""
Test script for Solana Contract Generator
This script tests the core functionality without GUI
"""

import sys
import os
import json
import tempfile
import shutil

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from solana_contract_generator import SolanaContractGenerator
import tkinter as tk

def test_wallet_generation():
    """Test wallet generation functionality"""
    print("Testing wallet generation...")
    
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    app = SolanaContractGenerator(root)
    
    try:
        private_key, public_key = app.generate_wallet()
        print(f"✓ Wallet generated successfully")
        print(f"  Public key length: {len(public_key)}")
        print(f"  Private key length: {len(private_key)}")
        return True
    except Exception as e:
        print(f"✗ Wallet generation failed: {e}")
        return False
    finally:
        root.destroy()

def test_metadata_generation():
    """Test metadata generation"""
    print("Testing metadata generation...")
    
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    app = SolanaContractGenerator(root)
    
    # Set test values
    app.name_var.set("Test Token")
    app.symbol_var.set("TEST")
    app.decimals_var.set(6)
    app.supply_var.set("1000000")
    app.description_var.set("Test token description")
    app.revoke_freeze_var.set(True)
    app.revoke_mint_var.set(False)
    app.network_var.set("devnet")
    app.wallet_option_var.set("generate")

    try:
        metadata = app.generate_metadata("test_hash_123")
        print(f"✓ Metadata generated successfully")
        print(f"  Name: {metadata['name']}")
        print(f"  Symbol: {metadata['symbol']}")
        print(f"  Supply: {metadata['supply']}")
        print(f"  Network: {metadata['network']}")
        print(f"  Token Hash: {metadata['token_hash']}")
        return True
    except Exception as e:
        print(f"✗ Metadata generation failed: {e}")
        return False
    finally:
        root.destroy()

def test_contract_generation():
    """Test smart contract generation"""
    print("Testing smart contract generation...")
    
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    app = SolanaContractGenerator(root)
    
    # Set test values
    app.name_var.set("Test Token")
    app.symbol_var.set("TEST")
    app.decimals_var.set(6)
    app.supply_var.set("1000000")
    app.description_var.set("Test token description")
    app.revoke_freeze_var.set(True)
    app.revoke_mint_var.set(False)
    app.network_var.set("devnet")
    app.wallet_option_var.set("generate")
    
    try:
        contract_code = app.generate_anchor_contract()
        print(f"✓ Smart contract generated successfully")
        print(f"  Contract length: {len(contract_code)} characters")
        
        # Check if contract contains expected elements
        if "use anchor_lang::prelude::*;" in contract_code:
            print("  ✓ Contains Anchor imports")
        if "declare_id!" in contract_code:
            print("  ✓ Contains program ID declaration")
        if "initialize_mint" in contract_code:
            print("  ✓ Contains initialize_mint function")
        if "mint_tokens" in contract_code:
            print("  ✓ Contains mint_tokens function")
            
        return True
    except Exception as e:
        print(f"✗ Smart contract generation failed: {e}")
        return False
    finally:
        root.destroy()

def test_validation():
    """Test input validation"""
    print("Testing input validation...")
    
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    app = SolanaContractGenerator(root)
    
    # Test empty inputs
    errors = app.validate_inputs()
    if "Symbol is required" in errors and "Supply is required" in errors:
        print("✓ Validation correctly catches empty required fields")
    else:
        print("✗ Validation failed to catch empty required fields")
        return False
    
    # Test valid inputs
    app.name_var.set("Test Token")
    app.symbol_var.set("TEST")
    app.supply_var.set("1000000")
    app.wallet_option_var.set("generate")
    
    errors = app.validate_inputs()
    if not errors:
        print("✓ Validation passes with valid inputs")
        return True
    else:
        print(f"✗ Validation failed with valid inputs: {errors}")
        return False
    
    root.destroy()

def main():
    """Run all tests"""
    print("=" * 50)
    print("Solana Contract Generator - Test Suite")
    print("=" * 50)
    
    tests = [
        test_wallet_generation,
        test_metadata_generation,
        test_contract_generation,
        test_validation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        print()
        if test():
            passed += 1
        print("-" * 30)
    
    print()
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The application is working correctly.")
        return True
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
