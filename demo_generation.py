#!/usr/bin/env python3
"""
Demo script showing automated contract generation
This demonstrates the core functionality without GUI
"""

import sys
import os
import json
import tkinter as tk

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from solana_contract_generator import SolanaContractGenerator

def demo_contract_generation():
    """Demonstrate contract generation with sample data"""
    print("🚀 Solana Contract Generator - Demo")
    print("=" * 50)
    
    # Create a hidden tkinter root (required for the class)
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    try:
        # Create the generator instance
        app = SolanaContractGenerator(root)
        
        # Set demo values
        print("📝 Setting demo token parameters...")
        app.name_var.set("Demo Token")
        app.symbol_var.set("DEMO")
        app.decimals_var.set(6)
        app.supply_var.set("1000000")
        app.description_var.set("Demo token for testing Solana contract generation")
        app.revoke_freeze_var.set(True)  # Allow liquidity pools
        app.revoke_mint_var.set(False)   # Keep mint authority
        app.network_var.set("devnet")
        app.wallet_option_var.set("generate")  # Generate new wallet

        print("✅ Parameters set:")
        print(f"   Name: {app.name_var.get()}")
        print(f"   Symbol: {app.symbol_var.get()}")
        print(f"   Decimals: {app.decimals_var.get()}")
        print(f"   Supply: {app.supply_var.get()}")
        print(f"   Network: {app.network_var.get()}")
        print(f"   Revoke Freeze: {app.revoke_freeze_var.get()}")
        print(f"   Revoke Mint: {app.revoke_mint_var.get()}")
        
        # Generate the contract
        print("\n🔧 Generating smart contract...")
        app.generate_contract()
        
        print("\n✅ Demo completed successfully!")
        print("\n📁 Check the 'generated_contract' folder for all files:")
        
        # List generated files
        output_dir = "generated_contract"
        if os.path.exists(output_dir):
            files = os.listdir(output_dir)
            for file in sorted(files):
                file_path = os.path.join(output_dir, file)
                size = os.path.getsize(file_path)
                print(f"   📄 {file} ({size} bytes)")
        
        print(f"\n🎯 Next steps:")
        print(f"   1. Review generated files in '{output_dir}/'")
        print(f"   2. Follow instructions in README.md")
        print(f"   3. Deploy to Solana devnet for testing")
        
    except Exception as e:
        print(f"❌ Error during demo: {str(e)}")
        return False
    finally:
        root.destroy()
    
    return True

if __name__ == "__main__":
    success = demo_contract_generation()
    sys.exit(0 if success else 1)
