#!/bin/bash

# Production Deployment Script for TEST Token
# Network: DEVNET
# Auto-generated by Solana Contract Generator

set -e  # Exit on any error

echo "Starting TEST Token Deployment"
echo "Network: DEVNET"
echo "=================================================="

# Check if Solana CLI is installed
if ! command -v solana &> /dev/null; then
    echo "ERROR: Solana CLI not found. Please install it first:"
    echo "   https://docs.solana.com/cli/install-solana-cli-tools"
    exit 1
fi

# Check if Anchor CLI is installed
if ! command -v anchor &> /dev/null; then
    echo "ERROR: Anchor CLI not found. Please install it first:"
    echo "   npm install -g @project-serum/anchor-cli"
    exit 1
fi

# Set Solana cluster
echo "Setting Solana network to devnet..."
solana config set --url devnet

# Check current configuration
echo "Current Solana configuration:"
solana config get

# Check wallet balance
echo "Checking wallet balance..."
BALANCE=$(solana balance --lamports)
echo "Current balance: $BALANCE lamports"

# Request airdrop if on devnet and balance is low
if [ "devnet" = "devnet" ]; then
    if [ "$BALANCE" -lt 1000000000 ]; then  # Less than 1 SOL
        echo "Low balance detected. Requesting airdrop..."
        solana airdrop 2
        echo "Airdrop completed"
    fi
fi

# Create Anchor project if it doesn't exist
if [ ! -f "Anchor.toml" ]; then
    echo "Creating Anchor project..."
    anchor init test_token --no-git
    cd test_token

    # Copy generated files
    echo "Copying generated contract files..."
    cp ../lib.rs programs/test_token/src/lib.rs
    cp ../Cargo.toml programs/test_token/Cargo.toml
    cp ../Anchor.toml ./Anchor.toml
else
    echo "Using existing Anchor project"
fi

# Build the program
echo "Building Anchor program..."
anchor build

# Deploy the program
echo "Deploying program to devnet..."
anchor deploy

# Get the program ID
PROGRAM_ID=$(solana address -k target/deploy/test_token-keypair.json)
echo "Program deployed successfully!"
echo "Program ID: $PROGRAM_ID"

# Update lib.rs with the actual program ID
echo "Updating lib.rs with deployed program ID..."
sed -i 's/declare_id!(".*");/declare_id!("'$PROGRAM_ID'");/' programs/test_token/src/lib.rs

# Rebuild with correct program ID
echo "Rebuilding with correct program ID..."
anchor build

# Redeploy with correct program ID
echo "Redeploying with correct program ID..."
anchor deploy

echo ""
echo "DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "=================================================="
echo "Token: TEST"
echo "Program ID: $PROGRAM_ID"
echo "Network: DEVNET"
echo "Supply: 1000000 tokens"
echo "Decimals: 6"
echo ""
echo "Next Steps:"
echo "1. Initialize token mint using the program"
echo "2. Mint initial supply: 1000000 tokens"
echo "4. Revoke freeze authority (as configured)"
echo ""
echo "Useful Commands:"
echo "  solana program show $PROGRAM_ID"
echo "  anchor test"
echo ""
