#!/bin/bash

# Deployment script for tst Token
# Network: DEVNET

echo "Starting deployment for tst Token on DEVNET..."

# Set Solana cluster
solana config set --url devnet

# Check Solana configuration
echo "Current Solana configuration:"
solana config get

# Check wallet balance
echo "Wallet balance:"
solana balance

# Build the program
echo "Building Anchor program..."
anchor build

# Deploy the program
echo "Deploying program..."
anchor deploy

# Get the program ID
PROGRAM_ID=$(solana address -k target/deploy/tst_token-keypair.json)
echo "Program deployed with ID: $PROGRAM_ID"

# Update lib.rs with the actual program ID
echo "Please update the program ID in lib.rs:"
echo "declare_id!(\"$PROGRAM_ID\");"

echo ""
echo "Deployment completed!"
echo ""
echo "Next steps:"
echo "1. Update lib.rs with the program ID shown above"
echo "2. Rebuild: anchor build"
echo "3. Redeploy: anchor deploy"
echo "4. Initialize token mint"
echo "5. Mint initial supply: 1000 tokens (1000000000 base units)"
echo "6. Revoke mint authority (as configured)"
echo "7. Revoke freeze authority (as configured)"
