{"name": "Demo <PERSON>", "symbol": "DEMO", "description": "Demo token for testing Solana contract generation", "decimals": 6, "supply": "1000000", "image": "", "token_hash": "Asx8HnxrGDZHrPSUGj7FspUyZbn58GcLzfYZo8moWTUX", "mint_address": "Asx8HnxrGDZHrPSUGj7FspUyZbn58GcLzfYZo8moWTUX", "revoke_freeze_authority": true, "revoke_mint_authority": false, "network": "devnet", "wallet_option": "generate", "created_at": "2025-06-19T05:56:29.837020", "generated_wallet": {"private_key": "********************************************", "public_key": "AmwhgeVTprotzx2HBGKCAhJ2sgAWZUDUVvJWRoevgtG1", "address": "AmwhgeVTprotzx2HBGKCAhJ2sgAWZUDUVvJWRoevgtG1"}}