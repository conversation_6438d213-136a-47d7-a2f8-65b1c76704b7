# Solana Smart Contract Generator - Packaging Instructions

This document provides complete instructions for running and packaging the Solana Smart Contract Generator as a standalone executable.

## Quick Start (Windows)

1. **Double-click `run_app.bat`** - This will automatically install dependencies and run the application.

## Manual Setup

### Prerequisites

- Python 3.7 or higher
- pip (Python package installer)

### Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application**:
   ```bash
   python solana_contract_generator.py
   ```

## Creating a Standalone Executable (.exe)

### Method 1: PyInstaller (Recommended)

1. **Install PyInstaller**:
   ```bash
   pip install pyinstaller
   ```

2. **Create executable**:
   ```bash
   pyinstaller --onefile --windowed --name "SolanaContractGenerator" solana_contract_generator.py
   ```

3. **Find your executable**:
   - The `.exe` file will be in the `dist/` folder
   - File name: `SolanaContractGenerator.exe`

### Method 2: Auto-py-to-exe (GUI Tool)

1. **Install auto-py-to-exe**:
   ```bash
   pip install auto-py-to-exe
   ```

2. **Launch GUI**:
   ```bash
   auto-py-to-exe
   ```

3. **Configure settings**:
   - Script Location: `solana_contract_generator.py`
   - Onefile: One File
   - Console Window: Window Based (hide the console)
   - Icon: (optional) Add an icon file

4. **Click "CONVERT .PY TO .EXE"**

### Method 3: cx_Freeze

1. **Install cx_Freeze**:
   ```bash
   pip install cx_Freeze
   ```

2. **Create setup.py**:
   ```python
   from cx_Freeze import setup, Executable
   
   setup(
       name="SolanaContractGenerator",
       version="1.0",
       description="Solana Smart Contract Generator",
       executables=[Executable("solana_contract_generator.py", base="Win32GUI")]
   )
   ```

3. **Build executable**:
   ```bash
   python setup.py build
   ```

## Packaging Options Comparison

| Method | Pros | Cons | File Size |
|--------|------|------|-----------|
| PyInstaller | Easy, reliable, widely used | Larger file size | ~50-80MB |
| auto-py-to-exe | GUI interface, user-friendly | Requires PyInstaller | ~50-80MB |
| cx_Freeze | Good cross-platform support | More complex setup | ~40-60MB |

## Distribution

### Single File Distribution

After creating the executable:

1. **Test the executable** on a clean system without Python installed
2. **Create a distribution folder** with:
   - Your `.exe` file
   - `README.md` (user instructions)
   - Example files or templates (optional)

### Installer Creation (Optional)

For professional distribution, create an installer using:

- **Inno Setup** (Windows): Free, powerful installer creator
- **NSIS** (Windows): Nullsoft Scriptable Install System
- **WiX Toolset** (Windows): Windows Installer XML toolset

## Troubleshooting

### Common Issues

1. **"Module not found" errors**:
   - Ensure all dependencies are installed
   - Use `--hidden-import` flag with PyInstaller if needed

2. **Large executable size**:
   - Use `--exclude-module` to exclude unnecessary modules
   - Consider using virtual environment for cleaner builds

3. **Antivirus false positives**:
   - This is common with PyInstaller executables
   - Consider code signing for distribution

### PyInstaller Advanced Options

```bash
# Exclude unnecessary modules to reduce size
pyinstaller --onefile --windowed \
    --exclude-module matplotlib \
    --exclude-module numpy \
    --exclude-module pandas \
    --name "SolanaContractGenerator" \
    solana_contract_generator.py

# Include additional files
pyinstaller --onefile --windowed \
    --add-data "icon.ico;." \
    --icon "icon.ico" \
    --name "SolanaContractGenerator" \
    solana_contract_generator.py
```

## Testing the Executable

1. **Test on development machine**:
   ```bash
   ./dist/SolanaContractGenerator.exe
   ```

2. **Test on clean machine**:
   - Copy executable to a machine without Python
   - Verify all functionality works
   - Test file generation and validation

3. **Test different scenarios**:
   - Generate new wallet
   - Use existing wallet
   - Different network selections
   - Various token configurations

## Performance Optimization

### Startup Time

- Use `--onedir` instead of `--onefile` for faster startup
- Consider lazy imports in the code

### File Size

- Use virtual environment for building
- Exclude unnecessary modules
- Use UPX compression (optional)

## Security Considerations

### Code Signing (Recommended for Distribution)

1. **Obtain code signing certificate**
2. **Sign the executable**:
   ```bash
   signtool sign /f certificate.p12 /p password /t http://timestamp.digicert.com SolanaContractGenerator.exe
   ```

### Antivirus Whitelisting

- Submit executable to major antivirus vendors
- Provide source code for verification
- Use established code signing certificate

## Final Checklist

- [ ] Application runs correctly from source
- [ ] All dependencies are included in requirements.txt
- [ ] Executable builds without errors
- [ ] Executable runs on clean system
- [ ] All features work in executable version
- [ ] File generation works correctly
- [ ] Error handling works properly
- [ ] User interface is responsive
- [ ] Documentation is complete

## Support

For issues with packaging:
1. Check PyInstaller documentation
2. Verify Python and dependency versions
3. Test in clean virtual environment
4. Check for platform-specific issues

---

*Last updated: 2025-06-19*
