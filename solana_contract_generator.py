#!/usr/bin/env python3
"""
Solana Smart Contract Generator Desktop App

This application generates Anchor smart contracts for Solana token creation.
It provides a GUI interface for inputting token parameters and generates
the necessary files including metadata.json and lib.rs.

To package as a single .exe file:
1. Install PyInstaller: pip install pyinstaller
2. Run: pyinstaller --onefile --windowed solana_contract_generator.py
3. The .exe will be in the dist/ folder

Dependencies:
- tkinter (built-in with Python)
- json (built-in)
- os (built-in)
- shutil (built-in)
- base58 (install with: pip install base58)
- cryptography (install with: pip install cryptography)
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import shutil
from datetime import datetime
import secrets
import base64

try:
    import base58
except ImportError:
    messagebox.showerror("Missing Dependency", "Please install base58: pip install base58")
    exit(1)

try:
    from cryptography.hazmat.primitives import hashes, serialization
    from cryptography.hazmat.primitives.asymmetric import ed25519
except ImportError:
    messagebox.showerror("Missing Dependency", "Please install cryptography: pip install cryptography")
    exit(1)


class SolanaContractGenerator:
    def __init__(self, root):
        self.root = root
        self.root.title("Solana Smart Contract Generator")
        self.root.geometry("800x900")
        self.root.resizable(True, True)
        
        # Variables
        self.symbol_var = tk.StringVar()
        self.decimals_var = tk.IntVar(value=6)
        self.supply_var = tk.StringVar()
        self.image_path_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.revoke_freeze_var = tk.BooleanVar()
        self.revoke_mint_var = tk.BooleanVar()
        self.show_more_var = tk.BooleanVar()
        self.network_var = tk.StringVar(value="devnet")
        self.wallet_option_var = tk.StringVar(value="generate")
        self.wallet_address_var = tk.StringVar()
        self.wallet_private_key_var = tk.StringVar()
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame with scrollbar
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="Solana Smart Contract Generator", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Basic fields frame
        basic_frame = ttk.LabelFrame(main_frame, text="Token Information", padding=10)
        basic_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Symbol
        ttk.Label(basic_frame, text="Symbol *:").grid(row=0, column=0, sticky=tk.W, pady=2)
        symbol_entry = ttk.Entry(basic_frame, textvariable=self.symbol_var, width=30)
        symbol_entry.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Decimals
        ttk.Label(basic_frame, text="Decimals:").grid(row=1, column=0, sticky=tk.W, pady=2)
        decimals_spin = ttk.Spinbox(basic_frame, from_=0, to=18, textvariable=self.decimals_var, width=28)
        decimals_spin.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Supply
        ttk.Label(basic_frame, text="Supply *:").grid(row=2, column=0, sticky=tk.W, pady=2)
        supply_entry = ttk.Entry(basic_frame, textvariable=self.supply_var, width=30)
        supply_entry.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Image
        ttk.Label(basic_frame, text="Image:").grid(row=3, column=0, sticky=tk.W, pady=2)
        image_frame = ttk.Frame(basic_frame)
        image_frame.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        image_entry = ttk.Entry(image_frame, textvariable=self.image_path_var, width=25)
        image_entry.pack(side=tk.LEFT)
        
        browse_btn = ttk.Button(image_frame, text="Browse", command=self.browse_image)
        browse_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # Description
        ttk.Label(basic_frame, text="Description:").grid(row=4, column=0, sticky=tk.NW, pady=2)
        desc_text = tk.Text(basic_frame, height=3, width=30)
        desc_text.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        desc_text.bind('<KeyRelease>', lambda e: self.description_var.set(desc_text.get("1.0", tk.END).strip()))
        
        # Checkboxes frame
        checkbox_frame = ttk.LabelFrame(main_frame, text="Authority Options", padding=10)
        checkbox_frame.pack(fill=tk.X, pady=(0, 10))
        
        freeze_cb = ttk.Checkbutton(checkbox_frame, text="Revoke Freeze Authority", 
                                   variable=self.revoke_freeze_var)
        freeze_cb.pack(anchor=tk.W)
        ttk.Label(checkbox_frame, text="  → Allows liquidity pool creation", 
                 font=("Arial", 8), foreground="gray").pack(anchor=tk.W)
        
        mint_cb = ttk.Checkbutton(checkbox_frame, text="Revoke Mint Authority", 
                                 variable=self.revoke_mint_var)
        mint_cb.pack(anchor=tk.W, pady=(5, 0))
        ttk.Label(checkbox_frame, text="  → Prevents future supply increases", 
                 font=("Arial", 8), foreground="gray").pack(anchor=tk.W)
        
        # Network frame
        network_frame = ttk.LabelFrame(main_frame, text="Network Configuration", padding=10)
        network_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(network_frame, text="Network:").grid(row=0, column=0, sticky=tk.W, pady=2)
        network_combo = ttk.Combobox(network_frame, textvariable=self.network_var, 
                                    values=["devnet", "mainnet"], state="readonly", width=27)
        network_combo.grid(row=0, column=1, sticky=tk.W, padx=(10, 0), pady=2)
        
        # Wallet frame
        wallet_frame = ttk.LabelFrame(main_frame, text="Wallet Configuration", padding=10)
        wallet_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Wallet option radio buttons
        ttk.Radiobutton(wallet_frame, text="Generate new wallet", 
                       variable=self.wallet_option_var, value="generate",
                       command=self.update_wallet_fields).pack(anchor=tk.W)
        
        ttk.Radiobutton(wallet_frame, text="Use existing wallet address", 
                       variable=self.wallet_option_var, value="address",
                       command=self.update_wallet_fields).pack(anchor=tk.W)
        
        ttk.Radiobutton(wallet_frame, text="Use existing private key", 
                       variable=self.wallet_option_var, value="private_key",
                       command=self.update_wallet_fields).pack(anchor=tk.W)
        
        # Wallet input fields
        self.wallet_address_frame = ttk.Frame(wallet_frame)
        self.wallet_address_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(self.wallet_address_frame, text="Wallet Address:").pack(anchor=tk.W)
        self.wallet_address_entry = ttk.Entry(self.wallet_address_frame, 
                                             textvariable=self.wallet_address_var, width=60)
        self.wallet_address_entry.pack(fill=tk.X, pady=(2, 0))
        
        self.wallet_private_key_frame = ttk.Frame(wallet_frame)
        self.wallet_private_key_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Label(self.wallet_private_key_frame, text="Private Key:").pack(anchor=tk.W)
        self.wallet_private_key_entry = ttk.Entry(self.wallet_private_key_frame, 
                                                 textvariable=self.wallet_private_key_var, 
                                                 width=60, show="*")
        self.wallet_private_key_entry.pack(fill=tk.X, pady=(2, 0))
        
        # Show more options
        more_frame = ttk.Frame(main_frame)
        more_frame.pack(fill=tk.X, pady=(0, 10))
        
        more_cb = ttk.Checkbutton(more_frame, text="Show More Options", 
                                 variable=self.show_more_var, command=self.toggle_more_options)
        more_cb.pack(anchor=tk.W)
        
        # Advanced options frame (initially hidden)
        self.advanced_frame = ttk.LabelFrame(main_frame, text="Advanced Options", padding=10)
        
        ttk.Label(self.advanced_frame, text="Additional configuration options will be added here").pack()
        
        # Generate button
        generate_btn = ttk.Button(main_frame, text="Generate Smart Contract", 
                                 command=self.generate_contract, style="Accent.TButton")
        generate_btn.pack(pady=20)
        
        # Status text
        self.status_text = scrolledtext.ScrolledText(main_frame, height=8, width=80)
        self.status_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Initialize UI state
        self.update_wallet_fields()
        self.toggle_more_options()
        
    def browse_image(self):
        filename = filedialog.askopenfilename(
            title="Select Token Image",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp"), ("All files", "*.*")]
        )
        if filename:
            self.image_path_var.set(filename)
            
    def update_wallet_fields(self):
        option = self.wallet_option_var.get()
        
        if option == "generate":
            self.wallet_address_frame.pack_forget()
            self.wallet_private_key_frame.pack_forget()
        elif option == "address":
            self.wallet_address_frame.pack(fill=tk.X, pady=(5, 0))
            self.wallet_private_key_frame.pack_forget()
        elif option == "private_key":
            self.wallet_address_frame.pack_forget()
            self.wallet_private_key_frame.pack(fill=tk.X, pady=(5, 0))
            
    def toggle_more_options(self):
        if self.show_more_var.get():
            self.advanced_frame.pack(fill=tk.X, pady=(0, 10))
        else:
            self.advanced_frame.pack_forget()
            
    def log_status(self, message):
        self.status_text.insert(tk.END, f"[{datetime.now().strftime('%H:%M:%S')}] {message}\n")
        self.status_text.see(tk.END)
        self.root.update()
        
    def validate_inputs(self):
        errors = []
        
        if not self.symbol_var.get().strip():
            errors.append("Symbol is required")
            
        if not self.supply_var.get().strip():
            errors.append("Supply is required")
        else:
            try:
                supply = float(self.supply_var.get())
                if supply <= 0:
                    errors.append("Supply must be greater than 0")
            except ValueError:
                errors.append("Supply must be a valid number")
                
        if self.wallet_option_var.get() == "address" and not self.wallet_address_var.get().strip():
            errors.append("Wallet address is required when using existing wallet")
            
        if self.wallet_option_var.get() == "private_key" and not self.wallet_private_key_var.get().strip():
            errors.append("Private key is required when using existing private key")
            
        return errors
        
    def generate_wallet(self):
        """Generate a new Ed25519 keypair for Solana"""
        private_key = ed25519.Ed25519PrivateKey.generate()
        public_key = private_key.public_key()
        
        # Get raw bytes
        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PrivateFormat.Raw,
            encryption_algorithm=serialization.NoEncryption()
        )
        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PublicFormat.Raw
        )
        
        # Encode to base58
        private_key_b58 = base58.b58encode(private_bytes).decode('utf-8')
        public_key_b58 = base58.b58encode(public_bytes).decode('utf-8')
        
        return private_key_b58, public_key_b58

    def generate_metadata(self):
        """Generate metadata.json file"""
        metadata = {
            "name": self.symbol_var.get().strip(),
            "symbol": self.symbol_var.get().strip(),
            "description": self.description_var.get().strip() or f"{self.symbol_var.get()} Token",
            "decimals": self.decimals_var.get(),
            "supply": self.supply_var.get().strip(),
            "image": os.path.basename(self.image_path_var.get()) if self.image_path_var.get() else "",
            "revoke_freeze_authority": self.revoke_freeze_var.get(),
            "revoke_mint_authority": self.revoke_mint_var.get(),
            "network": self.network_var.get(),
            "wallet_option": self.wallet_option_var.get(),
            "created_at": datetime.now().isoformat()
        }

        # Add wallet info based on option
        if self.wallet_option_var.get() == "address":
            metadata["wallet_address"] = self.wallet_address_var.get().strip()
        elif self.wallet_option_var.get() == "private_key":
            metadata["wallet_private_key"] = "***HIDDEN***"  # Don't store actual private key

        return metadata

    def generate_anchor_contract(self):
        """Generate the Anchor smart contract lib.rs file"""
        symbol = self.symbol_var.get().strip()
        decimals = self.decimals_var.get()
        supply = self.supply_var.get().strip()
        revoke_freeze = self.revoke_freeze_var.get()
        revoke_mint = self.revoke_mint_var.get()
        network = self.network_var.get()

        contract_code = f'''use anchor_lang::prelude::*;
use anchor_spl::token::{{self, MintTo, Token, TokenAccount, Mint}};
use anchor_spl::associated_token::AssociatedToken;

// Program ID - Replace with your actual program ID after deployment
declare_id!("********************************");

#[program]
pub mod {symbol.lower()}_token {{
    use super::*;

    /// Initialize the token mint
    pub fn initialize_mint(
        ctx: Context<InitializeMint>,
        decimals: u8,
        mint_authority: Pubkey,
        freeze_authority: Option<Pubkey>,
    ) -> Result<()> {{
        let cpi_accounts = token::InitializeMint {{
            mint: ctx.accounts.mint.to_account_info(),
            rent: ctx.accounts.rent.to_account_info(),
        }};
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::initialize_mint(cpi_ctx, decimals, &mint_authority, freeze_authority.as_ref())?;

        Ok(())
    }}

    /// Mint tokens to a destination account
    pub fn mint_tokens(
        ctx: Context<MintTokens>,
        amount: u64,
    ) -> Result<()> {{
        let cpi_accounts = MintTo {{
            mint: ctx.accounts.mint.to_account_info(),
            to: ctx.accounts.destination.to_account_info(),
            authority: ctx.accounts.authority.to_account_info(),
        }};
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::mint_to(cpi_ctx, amount)?;

        Ok(())
    }}

    /// Revoke mint authority (if enabled)
    pub fn revoke_mint_authority(ctx: Context<RevokeMintAuthority>) -> Result<()> {{
        let cpi_accounts = token::SetAuthority {{
            account_or_mint: ctx.accounts.mint.to_account_info(),
            current_authority: ctx.accounts.current_authority.to_account_info(),
        }};
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::set_authority(
            cpi_ctx,
            token::spl_token::instruction::AuthorityType::MintTokens,
            None,
        )?;

        Ok(())
    }}

    /// Revoke freeze authority (if enabled)
    pub fn revoke_freeze_authority(ctx: Context<RevokeFreezeAuthority>) -> Result<()> {{
        let cpi_accounts = token::SetAuthority {{
            account_or_mint: ctx.accounts.mint.to_account_info(),
            current_authority: ctx.accounts.current_authority.to_account_info(),
        }};
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::set_authority(
            cpi_ctx,
            token::spl_token::instruction::AuthorityType::FreezeAccount,
            None,
        )?;

        Ok(())
    }}
}}

#[derive(Accounts)]
pub struct InitializeMint<'info> {{
    #[account(
        init,
        payer = payer,
        mint::decimals = {decimals},
        mint::authority = payer,
        mint::freeze_authority = payer,
    )]
    pub mint: Account<'info, Mint>,
    #[account(mut)]
    pub payer: Signer<'info>,
    pub rent: Sysvar<'info, Rent>,
    pub system_program: Program<'info, System>,
    pub token_program: Program<'info, Token>,
}}

#[derive(Accounts)]
pub struct MintTokens<'info> {{
    #[account(mut)]
    pub mint: Account<'info, Mint>,
    #[account(
        init_if_needed,
        payer = authority,
        associated_token::mint = mint,
        associated_token::authority = authority,
    )]
    pub destination: Account<'info, TokenAccount>,
    #[account(mut)]
    pub authority: Signer<'info>,
    pub rent: Sysvar<'info, Rent>,
    pub system_program: Program<'info, System>,
    pub token_program: Program<'info, Token>,
    pub associated_token_program: Program<'info, AssociatedToken>,
}}

#[derive(Accounts)]
pub struct RevokeMintAuthority<'info> {{
    #[account(mut)]
    pub mint: Account<'info, Mint>,
    pub current_authority: Signer<'info>,
    pub token_program: Program<'info, Token>,
}}

#[derive(Accounts)]
pub struct RevokeFreezeAuthority<'info> {{
    #[account(mut)]
    pub mint: Account<'info, Mint>,
    pub current_authority: Signer<'info>,
    pub token_program: Program<'info, Token>,
}}

/*
TOKEN CONFIGURATION:
- Symbol: {symbol}
- Decimals: {decimals}
- Initial Supply: {supply}
- Network: {network.upper()}
- Revoke Freeze Authority: {revoke_freeze}
- Revoke Mint Authority: {revoke_mint}

DEPLOYMENT INSTRUCTIONS:
1. Install Anchor CLI: npm install -g @project-serum/anchor-cli
2. Install Solana CLI: https://docs.solana.com/cli/install-solana-cli-tools
3. Set network: solana config set --url {network}
4. Create new Anchor project: anchor init {symbol.lower()}_token
5. Replace lib.rs with this generated code
6. Update Anchor.toml with correct program ID
7. Build: anchor build
8. Deploy: anchor deploy
9. Update program ID in lib.rs with deployed program ID
10. Rebuild and redeploy: anchor build && anchor deploy

MINTING TOKENS:
After deployment, call mint_tokens instruction with:
- amount: {supply} * 10^{decimals} (for full supply)
- destination: your token account address

AUTHORITY REVOCATION:
{"- Call revoke_mint_authority() to prevent future minting" if revoke_mint else "- Mint authority retained (can mint more tokens)"}
{"- Call revoke_freeze_authority() to allow liquidity pools" if revoke_freeze else "- Freeze authority retained (can freeze accounts)"}
*/'''

        return contract_code

    def generate_contract(self):
        """Main function to generate the smart contract and metadata files"""
        try:
            # Clear status
            self.status_text.delete(1.0, tk.END)
            self.log_status("Starting contract generation...")

            # Validate inputs
            errors = self.validate_inputs()
            if errors:
                error_msg = "Validation errors:\n" + "\n".join(f"- {error}" for error in errors)
                messagebox.showerror("Validation Error", error_msg)
                self.log_status("Validation failed!")
                return

            self.log_status("Input validation passed")

            # Create output directory
            output_dir = "generated_contract"
            if os.path.exists(output_dir):
                shutil.rmtree(output_dir)
            os.makedirs(output_dir)
            self.log_status(f"Created output directory: {output_dir}")

            # Handle wallet generation if needed
            wallet_info = {}
            if self.wallet_option_var.get() == "generate":
                self.log_status("Generating new wallet...")
                try:
                    private_key, public_key = self.generate_wallet()
                    wallet_info = {
                        "private_key": private_key,
                        "public_key": public_key,
                        "address": public_key  # In Solana, public key is the address
                    }
                    self.log_status(f"Generated wallet address: {public_key}")

                    # Save wallet info to file
                    wallet_file = os.path.join(output_dir, "wallet_info.json")
                    with open(wallet_file, 'w') as f:
                        json.dump(wallet_info, f, indent=2)
                    self.log_status("Saved wallet information to wallet_info.json")

                except Exception as e:
                    self.log_status(f"Error generating wallet: {str(e)}")
                    messagebox.showerror("Wallet Generation Error", f"Failed to generate wallet: {str(e)}")
                    return

            # Generate metadata.json
            self.log_status("Generating metadata.json...")
            metadata = self.generate_metadata()
            if wallet_info:
                metadata["generated_wallet"] = wallet_info

            metadata_file = os.path.join(output_dir, "metadata.json")
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2)
            self.log_status("Generated metadata.json")

            # Copy image file if provided
            if self.image_path_var.get() and os.path.exists(self.image_path_var.get()):
                image_dest = os.path.join(output_dir, os.path.basename(self.image_path_var.get()))
                shutil.copy2(self.image_path_var.get(), image_dest)
                self.log_status(f"Copied image file: {os.path.basename(self.image_path_var.get())}")

            # Generate lib.rs
            self.log_status("Generating Anchor smart contract (lib.rs)...")
            contract_code = self.generate_anchor_contract()
            contract_file = os.path.join(output_dir, "lib.rs")
            with open(contract_file, 'w') as f:
                f.write(contract_code)
            self.log_status("Generated lib.rs")

            # Generate Cargo.toml
            self.log_status("Generating Cargo.toml...")
            cargo_toml = self.generate_cargo_toml()
            cargo_file = os.path.join(output_dir, "Cargo.toml")
            with open(cargo_file, 'w') as f:
                f.write(cargo_toml)
            self.log_status("Generated Cargo.toml")

            # Generate Anchor.toml
            self.log_status("Generating Anchor.toml...")
            anchor_toml = self.generate_anchor_toml()
            anchor_file = os.path.join(output_dir, "Anchor.toml")
            with open(anchor_file, 'w') as f:
                f.write(anchor_toml)
            self.log_status("Generated Anchor.toml")

            # Generate deployment script
            self.log_status("Generating deployment script...")
            deploy_script = self.generate_deploy_script()
            script_file = os.path.join(output_dir, "deploy.sh")
            with open(script_file, 'w') as f:
                f.write(deploy_script)
            self.log_status("Generated deploy.sh")

            # Generate README
            self.log_status("Generating README...")
            readme = self.generate_readme()
            readme_file = os.path.join(output_dir, "README.md")
            with open(readme_file, 'w') as f:
                f.write(readme)
            self.log_status("Generated README.md")

            self.log_status("=" * 50)
            self.log_status("CONTRACT GENERATION COMPLETED SUCCESSFULLY!")
            self.log_status("=" * 50)
            self.log_status(f"All files saved to: {os.path.abspath(output_dir)}")
            self.log_status("")
            self.log_status("Generated files:")
            self.log_status("- metadata.json (token metadata)")
            self.log_status("- lib.rs (Anchor smart contract)")
            self.log_status("- Cargo.toml (Rust dependencies)")
            self.log_status("- Anchor.toml (Anchor configuration)")
            self.log_status("- deploy.sh (deployment script)")
            self.log_status("- README.md (instructions)")
            if wallet_info:
                self.log_status("- wallet_info.json (generated wallet)")
            if self.image_path_var.get():
                self.log_status(f"- {os.path.basename(self.image_path_var.get())} (token image)")

            messagebox.showinfo("Success", f"Smart contract generated successfully!\\n\\nFiles saved to: {os.path.abspath(output_dir)}")

        except Exception as e:
            error_msg = f"Error generating contract: {str(e)}"
            self.log_status(error_msg)
            messagebox.showerror("Generation Error", error_msg)

    def generate_cargo_toml(self):
        """Generate Cargo.toml for the Anchor project"""
        symbol = self.symbol_var.get().strip().lower()
        return f'''[package]
name = "{symbol}_token"
version = "0.1.0"
description = "Solana token program for {self.symbol_var.get()}"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "{symbol}_token"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = []

[dependencies]
anchor-lang = "0.28.0"
anchor-spl = "0.28.0"

    def generate_anchor_toml(self):
        """Generate Anchor.toml configuration file"""
        symbol = self.symbol_var.get().strip().lower()
        network = self.network_var.get()
        cluster_url = "https://api.devnet.solana.com" if network == "devnet" else "https://api.mainnet-beta.solana.com"

        toml_content = f"""[features]
seeds = false
skip-lint = false

[programs.{network}]
{symbol}_token = "********************************"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "{cluster_url}"
wallet = "~/.config/solana/id.json"

[scripts]
test = "yarn run ts-mocha -p ./tsconfig.json -t 1000000 tests/**/*.ts"
"""
        return toml_content

    def generate_deploy_script(self):
        """Generate deployment script"""
        symbol = self.symbol_var.get().strip().lower()
        symbol_upper = self.symbol_var.get().strip()
        network = self.network_var.get()
        supply = self.supply_var.get().strip()
        decimals = self.decimals_var.get()

        script_content = f"""#!/bin/bash

# Deployment script for {symbol_upper} Token
# Network: {network.upper()}

echo "Starting deployment for {symbol_upper} Token on {network.upper()}..."

# Set Solana cluster
solana config set --url {network}

# Check Solana configuration
echo "Current Solana configuration:"
solana config get

# Check wallet balance
echo "Wallet balance:"
solana balance

# Build the program
echo "Building Anchor program..."
anchor build

# Deploy the program
echo "Deploying program..."
anchor deploy

# Get the program ID
PROGRAM_ID=$(solana address -k target/deploy/{symbol}_token-keypair.json)
echo "Program deployed with ID: $PROGRAM_ID"

# Update lib.rs with the actual program ID
echo "Please update the program ID in lib.rs:"
echo "declare_id!(\\"$PROGRAM_ID\\");"

echo ""
echo "Deployment completed!"
echo ""
echo "Next steps:"
echo "1. Update lib.rs with the program ID shown above"
echo "2. Rebuild: anchor build"
echo "3. Redeploy: anchor deploy"
echo "4. Initialize token mint"
echo "5. Mint initial supply: {supply} tokens ({supply}{'0' * decimals} base units)"
"""

        if self.revoke_mint_var.get():
            script_content += 'echo "6. Revoke mint authority (as configured)"\n'
        if self.revoke_freeze_var.get():
            script_content += 'echo "7. Revoke freeze authority (as configured)"\n'

        return script_content

    def generate_readme(self):
        """Generate comprehensive README file"""
        symbol = self.symbol_var.get().strip()
        network = self.network_var.get()
        supply = self.supply_var.get().strip()
        decimals = self.decimals_var.get()

        readme_content = f"""# {symbol} Token - Solana Smart Contract

This directory contains a complete Anchor smart contract for the {symbol} token on Solana.

## Token Configuration

- **Symbol**: {symbol}
- **Decimals**: {decimals}
- **Initial Supply**: {supply}
- **Network**: {network.upper()}
- **Revoke Freeze Authority**: {self.revoke_freeze_var.get()}
- **Revoke Mint Authority**: {self.revoke_mint_var.get()}

## Files Generated

- `lib.rs` - Main Anchor smart contract
- `Cargo.toml` - Rust dependencies
- `Anchor.toml` - Anchor framework configuration
- `metadata.json` - Token metadata
- `deploy.sh` - Deployment script
- `README.md` - This file"""

        if self.wallet_option_var.get() == "generate":
            readme_content += "\n- `wallet_info.json` - Generated wallet information"
        if self.image_path_var.get():
            readme_content += "\n- Token image file"

        readme_content += f"""

## Prerequisites

1. **Install Rust**: https://rustup.rs/
2. **Install Solana CLI**: https://docs.solana.com/cli/install-solana-cli-tools
3. **Install Anchor CLI**:
   ```bash
   npm install -g @project-serum/anchor-cli
   ```

## Setup Instructions

1. **Create new Anchor project**:
   ```bash
   anchor init {symbol.lower()}_token
   cd {symbol.lower()}_token
   ```

2. **Replace generated files**:
   - Copy `lib.rs` to `programs/{symbol.lower()}_token/src/lib.rs`
   - Copy `Cargo.toml` to `programs/{symbol.lower()}_token/Cargo.toml`
   - Copy `Anchor.toml` to project root

3. **Set Solana network**:
   ```bash
   solana config set --url {network}
   ```

4. **Configure wallet** (if using existing wallet):
   ```bash
   solana config set --keypair /path/to/your/keypair.json
   ```

## Deployment

1. **Build the program**:
   ```bash
   anchor build
   ```

2. **Deploy to {network}**:
   ```bash
   anchor deploy
   ```

3. **Update Program ID**:
   - Copy the program ID from deployment output
   - Update `declare_id!()` in `lib.rs`
   - Rebuild and redeploy

## Token Operations

### Initialize Token Mint
```bash
# Use Anchor client or write a script to call initialize_mint
# This creates the token mint account
```

### Mint Initial Supply
```bash
# Call mint_tokens instruction with amount: {supply}{'0' * decimals}
# This mints the full supply to your wallet
```

### Authority Management"""

        if self.revoke_mint_var.get():
            readme_content += "\n- **Revoke Mint Authority**: Call `revoke_mint_authority()` to prevent future minting"
        else:
            readme_content += "\n- **Mint Authority**: Retained - can mint additional tokens"

        if self.revoke_freeze_var.get():
            readme_content += "\n- **Revoke Freeze Authority**: Call `revoke_freeze_authority()` to allow liquidity pools"
        else:
            readme_content += "\n- **Freeze Authority**: Retained - can freeze token accounts"

        # Add testing section
        test_section = """

## Testing

Create tests in the `tests/` directory to verify contract functionality:

```typescript
// Example test structure
describe('""" + symbol.lower() + """_token', () => {
  it('Initializes token mint', async () => {
    // Test mint initialization
  });

  it('Mints tokens', async () => {
    // Test token minting
  });"""

        if self.revoke_mint_var.get():
            test_section += """

  it('Revokes mint authority', async () => {
    // Test mint authority revocation
  });"""

        if self.revoke_freeze_var.get():
            test_section += """

  it('Revokes freeze authority', async () => {
    // Test freeze authority revocation
  });"""

        test_section += """
});
```

"""
        readme_content += test_section

        # Add remaining sections
        readme_content += "## Security Considerations\n\n"
        readme_content += "- Always test on devnet before mainnet deployment\n"
        readme_content += "- Verify all authority settings before deployment\n"
        readme_content += "- Keep private keys secure\n"
        readme_content += "- Consider using multisig for production tokens\n\n"

        readme_content += "## Support\n\n"
        readme_content += "For issues with this generated contract:\n"
        readme_content += "1. Check Anchor documentation: https://book.anchor-lang.com/\n"
        readme_content += "2. Review Solana documentation: https://docs.solana.com/\n"
        readme_content += "3. Test thoroughly on devnet first\n\n"

        readme_content += "---\n\n"
        readme_content += "*Generated by Solana Contract Generator*\n"
        readme_content += "*Created: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + "*\n"

        return readme_content


def main():
    """Main application entry point"""
    root = tk.Tk()

    # Set application icon and styling
    try:
        root.tk.call('source', 'azure.tcl')
        root.tk.call('set_theme', 'light')
    except:
        pass  # Fallback to default theme if azure theme not available

    app = SolanaContractGenerator(root)

    # Center the window
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    # Start the application
    root.mainloop()


if __name__ == "__main__":
    main()
