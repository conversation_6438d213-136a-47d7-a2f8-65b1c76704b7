# Solana Smart Contract Generator

A complete desktop GUI application for generating Solana smart contracts using the Anchor framework. This tool allows you to create token contracts with customizable parameters through an intuitive interface.

## 🚀 Features

- **Desktop GUI Interface** - Easy-to-use graphical interface built with tkinter
- **Token Configuration** - Set symbol, decimals, supply, description, and image
- **Authority Management** - Configure mint and freeze authority settings
- **Network Selection** - Choose between Devnet and Mainnet
- **Wallet Options** - Generate new wallet, use existing address, or import private key
- **Complete Contract Generation** - Generates all necessary files for deployment
- **Validation** - Input validation to ensure correct parameters
- **Packaging Ready** - Can be compiled to a single .exe file

## 📁 Generated Files

When you click "Generate Smart Contract", the application creates:

- `lib.rs` - Complete Anchor smart contract
- `metadata.json` - Token metadata and configuration
- `Cargo.toml` - Rust project dependencies
- `Anchor.toml` - Anchor framework configuration
- `deploy.sh` - Deployment script with instructions
- `README.md` - Comprehensive setup and deployment guide
- `wallet_info.json` - Generated wallet information (if applicable)
- Token image file (if provided)

## 🛠️ Installation & Setup

### Quick Start (Windows)
```bash
# Double-click to run
run_app.bat
```

### Manual Installation
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python solana_contract_generator.py
```

## 📋 Usage Instructions

1. **Launch the Application**
   - Run `python solana_contract_generator.py` or use `run_app.bat`

2. **Fill in Token Information**
   - **Symbol**: Your token's symbol (required)
   - **Decimals**: Number of decimal places (default: 6)
   - **Supply**: Total token supply (required)
   - **Image**: Optional token image file
   - **Description**: Token description

3. **Configure Authorities**
   - **Revoke Freeze Authority**: Check to allow liquidity pools
   - **Revoke Mint Authority**: Check to prevent future minting

4. **Select Network**
   - Choose between Devnet (testing) or Mainnet (production)

5. **Configure Wallet**
   - **Generate new wallet**: Creates a new keypair
   - **Use existing address**: Input your wallet address
   - **Use private key**: Import existing private key

6. **Generate Contract**
   - Click "Generate Smart Contract"
   - All files will be saved to `generated_contract/` folder

## 🔧 Creating an Executable

### Using PyInstaller (Recommended)
```bash
# Install PyInstaller
pip install pyinstaller

# Create executable
pyinstaller --onefile --windowed --name "SolanaContractGenerator" solana_contract_generator.py

# Find your .exe in dist/ folder
```

### Using auto-py-to-exe (GUI Method)
```bash
# Install and launch GUI tool
pip install auto-py-to-exe
auto-py-to-exe

# Configure settings in the GUI and convert
```

## 📝 Example Usage

1. **Create a Test Token**
   - Symbol: `MYTOKEN`
   - Decimals: `6`
   - Supply: `1000000`
   - Network: `Devnet`
   - Generate new wallet

2. **Deploy the Contract**
   - Follow instructions in generated `README.md`
   - Use the provided `deploy.sh` script
   - Update program ID after deployment

## 🔒 Security Features

- **Input Validation**: Ensures all required fields are filled
- **Network Selection**: Separate devnet/mainnet configurations
- **Authority Management**: Configurable mint and freeze authorities
- **Private Key Handling**: Secure wallet generation and import
- **Comprehensive Documentation**: Detailed deployment instructions

## 🧪 Testing

The application includes a comprehensive test suite:

```bash
# Run tests
python test_app.py
```

Tests cover:
- Wallet generation functionality
- Metadata creation
- Smart contract generation
- Input validation

## 📚 Generated Smart Contract Features

The generated Anchor smart contract includes:

- **Token Mint Initialization**: Creates the token mint account
- **Token Minting**: Mint tokens to specified accounts
- **Authority Revocation**: Optional mint/freeze authority revocation
- **Comprehensive Documentation**: Inline comments and instructions
- **Security Best Practices**: Following Solana/Anchor standards

## 🔗 Dependencies

- `tkinter` - GUI framework (built-in with Python)
- `base58` - Base58 encoding for Solana addresses
- `cryptography` - Cryptographic functions for wallet generation
- `json`, `os`, `shutil` - Standard library modules

## 📖 Documentation

- `PACKAGING_INSTRUCTIONS.md` - Detailed packaging guide
- Generated `README.md` - Contract-specific instructions
- Inline code comments - Comprehensive code documentation

## 🤝 Support

For issues or questions:
1. Check the generated README.md for contract-specific help
2. Review Anchor documentation: https://book.anchor-lang.com/
3. Consult Solana documentation: https://docs.solana.com/

## ⚠️ Important Notes

- **Always test on Devnet first** before deploying to Mainnet
- **Keep private keys secure** - Never share or commit them
- **Verify all settings** before contract deployment
- **Understand authority implications** before revoking authorities

---

*Built for Solana blockchain development with Anchor framework*
