#!/usr/bin/env python3
"""
Test script to debug deployment script generation
"""

import sys
import os
import tkinter as tk

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from solana_contract_generator import SolanaContractGenerator

def test_deploy_script():
    """Test deployment script generation"""
    print("Testing deployment script generation...")
    
    root = tk.Tk()
    root.withdraw()  # Hide the window
    
    try:
        app = SolanaContractGenerator(root)
        
        # Set test values
        app.name_var.set("Test Token")
        app.symbol_var.set("TEST")
        app.decimals_var.set(6)
        app.supply_var.set("1000000")
        app.network_var.set("devnet")
        app.revoke_freeze_var.set(True)
        app.revoke_mint_var.set(False)
        
        # Generate deployment script
        script_content = app.generate_real_deploy_script()
        
        print(f"Script length: {len(script_content)} characters")
        print("First 200 characters:")
        print(script_content[:200])
        print("...")
        print("Last 200 characters:")
        print(script_content[-200:])
        
        # Save to test file
        with open("test_deploy.sh", "w") as f:
            f.write(script_content)
        print("Script saved to test_deploy.sh")
        
        return True
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        root.destroy()

if __name__ == "__main__":
    test_deploy_script()
