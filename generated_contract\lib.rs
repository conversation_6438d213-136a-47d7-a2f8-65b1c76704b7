use anchor_lang::prelude::*;
use anchor_spl::token::{self, <PERSON><PERSON><PERSON>, Token, TokenAccount, Mint};
use anchor_spl::associated_token::AssociatedToken;

// Program ID - Replace with your actual program ID after deployment
declare_id!("11111111111111111111111111111111");

#[program]
pub mod tst_token {
    use super::*;

    /// Initialize the token mint
    pub fn initialize_mint(
        ctx: Context<InitializeMint>,
        decimals: u8,
        mint_authority: Pubkey,
        freeze_authority: Option<Pubkey>,
    ) -> Result<()> {
        let cpi_accounts = token::InitializeMint {
            mint: ctx.accounts.mint.to_account_info(),
            rent: ctx.accounts.rent.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::initialize_mint(cpi_ctx, decimals, &mint_authority, freeze_authority.as_ref())?;

        Ok(())
    }

    /// Mint tokens to a destination account
    pub fn mint_tokens(
        ctx: Context<MintTokens>,
        amount: u64,
    ) -> Result<()> {
        let cpi_accounts = MintTo {
            mint: ctx.accounts.mint.to_account_info(),
            to: ctx.accounts.destination.to_account_info(),
            authority: ctx.accounts.authority.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::mint_to(cpi_ctx, amount)?;

        Ok(())
    }

    /// Revoke mint authority (if enabled)
    pub fn revoke_mint_authority(ctx: Context<RevokeMintAuthority>) -> Result<()> {
        let cpi_accounts = token::SetAuthority {
            account_or_mint: ctx.accounts.mint.to_account_info(),
            current_authority: ctx.accounts.current_authority.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::set_authority(
            cpi_ctx,
            token::spl_token::instruction::AuthorityType::MintTokens,
            None,
        )?;

        Ok(())
    }

    /// Revoke freeze authority (if enabled)
    pub fn revoke_freeze_authority(ctx: Context<RevokeFreezeAuthority>) -> Result<()> {
        let cpi_accounts = token::SetAuthority {
            account_or_mint: ctx.accounts.mint.to_account_info(),
            current_authority: ctx.accounts.current_authority.to_account_info(),
        };
        let cpi_program = ctx.accounts.token_program.to_account_info();
        let cpi_ctx = CpiContext::new(cpi_program, cpi_accounts);

        token::set_authority(
            cpi_ctx,
            token::spl_token::instruction::AuthorityType::FreezeAccount,
            None,
        )?;

        Ok(())
    }
}

#[derive(Accounts)]
pub struct InitializeMint<'info> {
    #[account(
        init,
        payer = payer,
        mint::decimals = 6,
        mint::authority = payer,
        mint::freeze_authority = payer,
    )]
    pub mint: Account<'info, Mint>,
    #[account(mut)]
    pub payer: Signer<'info>,
    pub rent: Sysvar<'info, Rent>,
    pub system_program: Program<'info, System>,
    pub token_program: Program<'info, Token>,
}

#[derive(Accounts)]
pub struct MintTokens<'info> {
    #[account(mut)]
    pub mint: Account<'info, Mint>,
    #[account(
        init_if_needed,
        payer = authority,
        associated_token::mint = mint,
        associated_token::authority = authority,
    )]
    pub destination: Account<'info, TokenAccount>,
    #[account(mut)]
    pub authority: Signer<'info>,
    pub rent: Sysvar<'info, Rent>,
    pub system_program: Program<'info, System>,
    pub token_program: Program<'info, Token>,
    pub associated_token_program: Program<'info, AssociatedToken>,
}

#[derive(Accounts)]
pub struct RevokeMintAuthority<'info> {
    #[account(mut)]
    pub mint: Account<'info, Mint>,
    pub current_authority: Signer<'info>,
    pub token_program: Program<'info, Token>,
}

#[derive(Accounts)]
pub struct RevokeFreezeAuthority<'info> {
    #[account(mut)]
    pub mint: Account<'info, Mint>,
    pub current_authority: Signer<'info>,
    pub token_program: Program<'info, Token>,
}

/*
TOKEN CONFIGURATION:
- Symbol: tst
- Decimals: 6
- Initial Supply: 1000
- Network: DEVNET
- Revoke Freeze Authority: True
- Revoke Mint Authority: True

DEPLOYMENT INSTRUCTIONS:
1. Install Anchor CLI: npm install -g @project-serum/anchor-cli
2. Install Solana CLI: https://docs.solana.com/cli/install-solana-cli-tools
3. Set network: solana config set --url devnet
4. Create new Anchor project: anchor init tst_token
5. Replace lib.rs with this generated code
6. Update Anchor.toml with correct program ID
7. Build: anchor build
8. Deploy: anchor deploy
9. Update program ID in lib.rs with deployed program ID
10. Rebuild and redeploy: anchor build && anchor deploy

MINTING TOKENS:
After deployment, call mint_tokens instruction with:
- amount: 1000 * 10^6 (for full supply)
- destination: your token account address

AUTHORITY REVOCATION:
- Call revoke_mint_authority() to prevent future minting
- Call revoke_freeze_authority() to allow liquidity pools
*/