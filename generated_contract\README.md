# tst Token - Solana Smart Contract

This directory contains a complete Anchor smart contract for the tst token on Solana.

## Token Configuration

- **Symbol**: tst
- **Decimals**: 6
- **Initial Supply**: 1000
- **Network**: DEVNET
- **Revoke Freeze Authority**: True
- **Revoke Mint Authority**: True

## Files Generated

- `lib.rs` - Main Anchor smart contract
- `Cargo.toml` - Rust dependencies
- `Anchor.toml` - Anchor framework configuration
- `metadata.json` - Token metadata
- `deploy.sh` - Deployment script
- `README.md` - This file
- `wallet_info.json` - Generated wallet information
- Token image file

## Prerequisites

1. **Install Rust**: https://rustup.rs/
2. **Install Solana CLI**: https://docs.solana.com/cli/install-solana-cli-tools
3. **Install Anchor CLI**:
   ```bash
   npm install -g @project-serum/anchor-cli
   ```

## Setup Instructions

1. **Create new Anchor project**:
   ```bash
   anchor init tst_token
   cd tst_token
   ```

2. **Replace generated files**:
   - Copy `lib.rs` to `programs/tst_token/src/lib.rs`
   - Copy `Cargo.toml` to `programs/tst_token/Cargo.toml`
   - Copy `Anchor.toml` to project root

3. **Set Solana network**:
   ```bash
   solana config set --url devnet
   ```

4. **Configure wallet** (if using existing wallet):
   ```bash
   solana config set --keypair /path/to/your/keypair.json
   ```

## Deployment

1. **Build the program**:
   ```bash
   anchor build
   ```

2. **Deploy to devnet**:
   ```bash
   anchor deploy
   ```

3. **Update Program ID**:
   - Copy the program ID from deployment output
   - Update `declare_id!()` in `lib.rs`
   - Rebuild and redeploy

## Token Operations

### Initialize Token Mint
```bash
# Use Anchor client or write a script to call initialize_mint
# This creates the token mint account
```

### Mint Initial Supply
```bash
# Call mint_tokens instruction with amount: **********
# This mints the full supply to your wallet
```

### Authority Management
- **Revoke Mint Authority**: Call `revoke_mint_authority()` to prevent future minting
- **Revoke Freeze Authority**: Call `revoke_freeze_authority()` to allow liquidity pools

## Testing

Create tests in the `tests/` directory to verify contract functionality.

Use Anchor's testing framework to write comprehensive tests for your token contract.

Example test areas to cover:
- Token mint initialization
- Token minting functionality
- Mint authority revocation
- Freeze authority revocation

Refer to Anchor documentation for detailed testing examples.

## Security Considerations

- Always test on devnet before mainnet deployment
- Verify all authority settings before deployment
- Keep private keys secure
- Consider using multisig for production tokens

## Support

For issues with this generated contract:
1. Check Anchor documentation: https://book.anchor-lang.com/
2. Review Solana documentation: https://docs.solana.com/
3. Test thoroughly on devnet first

---

*Generated by Solana Contract Generator*
*Created: 2025-06-19 05:49:30*
